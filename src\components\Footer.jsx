import { motion } from 'framer-motion';

const Footer = () => {
  return (
    <footer style={{
      background: 'var(--secondary-bg)',
      padding: '4rem 2rem 2rem',
      borderTop: '1px solid rgba(139, 92, 246, 0.2)'
    }}>
      <div style={{
        maxWidth: '1200px',
        margin: '0 auto',
        textAlign: 'center'
      }}>
        <motion.div
          style={{
            marginBottom: '2rem'
          }}
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <h3 style={{
            background: 'linear-gradient(135deg, var(--accent-purple), var(--accent-magenta))',
            backgroundClip: 'text',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            marginBottom: '1rem',
            fontSize: '2rem'
          }}>
            Bindery
          </h3>
          <p style={{
            color: 'var(--text-secondary)',
            maxWidth: '500px',
            margin: '0 auto'
          }}>
            Growing digital experiences from the roots up. 
            Every great forest starts with a single seed.
          </p>
        </motion.div>

        <motion.div
          style={{
            display: 'flex',
            justifyContent: 'center',
            gap: '2rem',
            marginBottom: '2rem',
            flexWrap: 'wrap'
          }}
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          {['About', 'Services', 'Projects', 'Contact'].map((link) => (
            <motion.a
              key={link}
              href={`#${link.toLowerCase()}`}
              style={{
                color: 'var(--text-secondary)',
                textDecoration: 'none',
                fontSize: '1rem',
                fontWeight: '500'
              }}
              whileHover={{
                color: 'var(--accent-purple)',
                scale: 1.05
              }}
              data-cursor-hover
            >
              {link}
            </motion.a>
          ))}
        </motion.div>

        <motion.div
          style={{
            paddingTop: '2rem',
            borderTop: '1px solid rgba(139, 92, 246, 0.1)',
            color: 'var(--text-muted)',
            fontSize: '0.9rem'
          }}
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ duration: 0.6, delay: 0.4 }}
        >
          <p>© 2025 Bindery Studio. Crafted with 💜 and lots of ☕</p>
          <p style={{ marginTop: '0.5rem' }}>
            Built with React, GSAP, and Framer Motion
          </p>
        </motion.div>
      </div>
    </footer>
  );
};

export default Footer;
