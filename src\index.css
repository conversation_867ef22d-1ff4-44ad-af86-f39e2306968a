:root {
  /* Color Palette - Vibrant colors on dark background */
  --primary-bg: #0a0a0a;
  --secondary-bg: #1a1a1a;
  --accent-purple: #8b5cf6;
  --accent-magenta: #ec4899;
  --accent-cyan: #06b6d4;
  --accent-orange: #f97316;
  --text-primary: #ffffff;
  --text-secondary: #a1a1aa;
  --text-muted: #71717a;

  /* Typography */
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    sans-serif;
  line-height: 1.6;
  font-weight: 400;

  /* Smooth scrolling */
  scroll-behavior: smooth;

  /* Performance optimizations */
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  background-color: var(--primary-bg);
  color: var(--text-primary);
  overflow-x: hidden;
  min-height: 100vh;
}

/* Custom cursor styles */
body {
  cursor: none;
}

/* Typography Scale */
h1 {
  font-size: clamp(3rem, 8vw, 8rem);
  font-weight: 800;
  line-height: 0.9;
  letter-spacing: -0.02em;
}

h2 {
  font-size: clamp(2rem, 5vw, 4rem);
  font-weight: 700;
  line-height: 1.1;
  letter-spacing: -0.01em;
}

h3 {
  font-size: clamp(1.5rem, 3vw, 2.5rem);
  font-weight: 600;
  line-height: 1.2;
}

p {
  font-size: clamp(1rem, 2vw, 1.25rem);
  line-height: 1.6;
  color: var(--text-secondary);
}

/* Smooth animations */
* {
  transition: color 0.3s ease, background-color 0.3s ease;
}

/* Hide scrollbar but keep functionality */
::-webkit-scrollbar {
  width: 0px;
  background: transparent;
}

/* Selection styles */
::selection {
  background: var(--accent-magenta);
  color: white;
}

/* Mobile optimizations */
@media (max-width: 768px) {
  body {
    cursor: auto; /* Show default cursor on mobile */
  }

  .custom-cursor,
  .cursor-dot {
    display: none; /* Hide custom cursor on mobile */
  }

  h1 {
    font-size: clamp(2.5rem, 10vw, 6rem);
  }

  h2 {
    font-size: clamp(1.8rem, 8vw, 3rem);
  }
}

/* Smooth focus transitions */
input:focus,
textarea:focus,
button:focus {
  outline: none;
}

/* Loading animation for better UX */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

/* Additional responsive improvements */
@media (max-width: 480px) {
  :root {
    --section-padding: 4rem 1rem;
  }

  h1 {
    font-size: clamp(2rem, 12vw, 4rem);
  }

  .hero-section {
    padding: 2rem 1rem;
  }

  /* Adjust grid layouts for mobile */
  [style*="grid-template-columns"] {
    grid-template-columns: 1fr !important;
    gap: 2rem !important;
  }
}

/* Improve accessibility */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  .custom-cursor,
  .cursor-dot {
    display: none !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --text-primary: #ffffff;
    --text-secondary: #ffffff;
    --accent-purple: #bb86fc;
    --accent-magenta: #ff79c6;
  }
}
