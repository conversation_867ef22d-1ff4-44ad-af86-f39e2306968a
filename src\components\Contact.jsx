import { useEffect, useRef } from "react";
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import { motion } from "framer-motion";

gsap.registerPlugin(ScrollTrigger);

const Contact = () => {
  const sectionRef = useRef(null);
  const titleRef = useRef(null);
  const contentRef = useRef(null);
  const formRef = useRef(null);

  useEffect(() => {
    const section = sectionRef.current;
    const title = titleRef.current;
    const content = contentRef.current;
    const form = formRef.current;

    if (!section || !title || !content || !form) return;

    // Create timeline for sequential animations
    const tl = gsap.timeline({
      scrollTrigger: {
        trigger: section,
        start: "top 70%",
        end: "bottom 20%",
        toggleActions: "play none none reverse",
      },
    });

    tl.fromTo(
      title,
      { y: 100, opacity: 0 },
      { y: 0, opacity: 1, duration: 1.2, ease: "power3.out" }
    )
      .fromTo(
        content,
        { y: 80, opacity: 0 },
        { y: 0, opacity: 1, duration: 1, ease: "power3.out" },
        "-=0.6"
      )
      .fromTo(
        form,
        { y: 100, opacity: 0, scale: 0.9 },
        { y: 0, opacity: 1, scale: 1, duration: 1.2, ease: "power3.out" },
        "-=0.4"
      );

    return () => {
      ScrollTrigger.getAll().forEach((trigger) => trigger.kill());
    };
  }, []);

  return (
    <section
      id="contact"
      ref={sectionRef}
      style={{
        minHeight: "100vh",
        padding: "8rem 2rem",
        background: `
          linear-gradient(135deg, var(--secondary-bg) 0%, var(--primary-bg) 100%),
          radial-gradient(circle at 20% 80%, var(--accent-purple)20 0%, transparent 50%),
          radial-gradient(circle at 80% 20%, var(--accent-magenta)20 0%, transparent 50%)
        `,
        position: "relative",
        display: "flex",
        alignItems: "center",
      }}
    >
      <div
        style={{
          maxWidth: "1200px",
          margin: "0 auto",
          width: "100%",
        }}
      >
        <div
          style={{
            display: "grid",
            gridTemplateColumns: "repeat(auto-fit, minmax(400px, 1fr))",
            gap: "6rem",
            alignItems: "center",
          }}
        >
          {/* Left side - Content */}
          <div>
            <motion.h2
              ref={titleRef}
              style={{
                marginBottom: "2rem",
                background: `linear-gradient(135deg, var(--accent-purple), var(--accent-cyan))`,
                backgroundClip: "text",
                WebkitBackgroundClip: "text",
                WebkitTextFillColor: "transparent",
                fontSize: "clamp(2.5rem, 5vw, 4rem)",
              }}
            >
              Let's Plant Something Amazing Together
            </motion.h2>

            <div ref={contentRef}>
              <p
                style={{
                  fontSize: "clamp(1.1rem, 2vw, 1.4rem)",
                  lineHeight: 1.8,
                  color: "var(--text-secondary)",
                  marginBottom: "3rem",
                }}
              >
                Every great forest starts with a single seed. Share your vision
                with us, and let's cultivate it into something extraordinary.
              </p>

              <div style={{ marginBottom: "2rem" }}>
                <h3
                  style={{
                    color: "var(--text-primary)",
                    marginBottom: "1rem",
                    fontSize: "1.3rem",
                  }}
                >
                  Get in Touch
                </h3>
                <div
                  style={{ color: "var(--text-secondary)", lineHeight: 1.8 }}
                >
                  <p>📧 <EMAIL></p>
                  <p>📱 +1 (555) 123-4567</p>
                  <p>📍 Growing ideas worldwide</p>
                </div>
              </div>

              <div
                style={{
                  display: "flex",
                  gap: "1rem",
                  marginTop: "2rem",
                }}
              >
                {["Twitter", "LinkedIn", "Instagram"].map((social, index) => (
                  <motion.button
                    key={social}
                    style={{
                      background: "transparent",
                      border: "2px solid var(--accent-purple)",
                      color: "var(--accent-purple)",
                      padding: "0.8rem 1.5rem",
                      borderRadius: "50px",
                      cursor: "pointer",
                      fontWeight: "600",
                    }}
                    whileHover={{
                      background: "var(--accent-purple)",
                      color: "white",
                      scale: 1.05,
                    }}
                    whileTap={{ scale: 0.95 }}
                    data-cursor-hover
                  >
                    {social}
                  </motion.button>
                ))}
              </div>
            </div>
          </div>

          {/* Right side - Contact Form */}
          <motion.div
            ref={formRef}
            style={{
              background: "rgba(26, 26, 26, 0.8)",
              padding: "3rem",
              borderRadius: "24px",
              border: "1px solid rgba(139, 92, 246, 0.2)",
              backdropFilter: "blur(20px)",
            }}
          >
            <h3
              style={{
                color: "var(--text-primary)",
                marginBottom: "2rem",
                fontSize: "1.5rem",
              }}
            >
              Start Your Project
            </h3>

            <form
              style={{
                display: "flex",
                flexDirection: "column",
                gap: "1.5rem",
              }}
            >
              <motion.input
                type="text"
                placeholder="Your Name"
                style={{
                  background: "rgba(139, 92, 246, 0.1)",
                  border: "1px solid rgba(139, 92, 246, 0.3)",
                  borderRadius: "12px",
                  padding: "1rem",
                  color: "var(--text-primary)",
                  fontSize: "1rem",
                }}
                whileFocus={{
                  borderColor: "var(--accent-purple)",
                  boxShadow: "0 0 0 3px rgba(139, 92, 246, 0.1)",
                }}
                data-cursor-hover
              />

              <motion.input
                type="email"
                placeholder="Your Email"
                style={{
                  background: "rgba(139, 92, 246, 0.1)",
                  border: "1px solid rgba(139, 92, 246, 0.3)",
                  borderRadius: "12px",
                  padding: "1rem",
                  color: "var(--text-primary)",
                  fontSize: "1rem",
                }}
                whileFocus={{
                  borderColor: "var(--accent-purple)",
                  boxShadow: "0 0 0 3px rgba(139, 92, 246, 0.1)",
                }}
                data-cursor-hover
              />

              <motion.textarea
                placeholder="Tell us about your project..."
                rows="5"
                style={{
                  background: "rgba(139, 92, 246, 0.1)",
                  border: "1px solid rgba(139, 92, 246, 0.3)",
                  borderRadius: "12px",
                  padding: "1rem",
                  color: "var(--text-primary)",
                  fontSize: "1rem",
                  resize: "vertical",
                  fontFamily: "inherit",
                }}
                whileFocus={{
                  borderColor: "var(--accent-purple)",
                  boxShadow: "0 0 0 3px rgba(139, 92, 246, 0.1)",
                }}
                data-cursor-hover
              />

              <motion.button
                type="submit"
                style={{
                  background:
                    "linear-gradient(135deg, var(--accent-purple), var(--accent-magenta))",
                  border: "none",
                  borderRadius: "12px",
                  padding: "1.2rem",
                  color: "white",
                  fontSize: "1.1rem",
                  fontWeight: "600",
                  cursor: "pointer",
                  marginTop: "1rem",
                }}
                whileHover={{
                  scale: 1.02,
                  boxShadow: "0 10px 30px rgba(139, 92, 246, 0.3)",
                }}
                whileTap={{ scale: 0.98 }}
                data-cursor-hover
              >
                Plant the Seed 🌱
              </motion.button>
            </form>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default Contact;
