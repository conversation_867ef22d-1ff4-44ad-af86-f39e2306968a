import { useEffect } from "react";
import CustomCursor from "./components/CustomCursor";
import Navigation from "./components/Navigation";
import Hero from "./components/Hero";
import About from "./components/About";
import Projects from "./components/Projects";
import Contact from "./components/Contact";
import Footer from "./components/Footer";

function App() {
  useEffect(() => {
    // Smooth scrolling polyfill for older browsers
    if (!("scrollBehavior" in document.documentElement.style)) {
      import("smoothscroll-polyfill").then((smoothscroll) => {
        smoothscroll.polyfill();
      });
    }
  }, []);

  return (
    <div className="app">
      <CustomCursor />
      <Navigation />
      <Hero />
      <About />
      <Projects />
      <Contact />
      <Footer />
    </div>
  );
}

export default App;
