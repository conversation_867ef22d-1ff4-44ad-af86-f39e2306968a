// Performance optimization utilities

// Debounce function for scroll events
export const debounce = (func, wait) => {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

// Throttle function for high-frequency events
export const throttle = (func, limit) => {
  let inThrottle;
  return function() {
    const args = arguments;
    const context = this;
    if (!inThrottle) {
      func.apply(context, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
};

// Check if user prefers reduced motion
export const prefersReducedMotion = () => {
  return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
};

// Optimize GSAP animations for performance
export const getOptimizedGSAPConfig = () => {
  return {
    force3D: true,
    transformOrigin: "center center",
    ease: "power2.out",
    duration: prefersReducedMotion() ? 0.1 : 1
  };
};

// Lazy load images
export const lazyLoadImage = (src, placeholder = '') => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => resolve(src);
    img.onerror = reject;
    img.src = src;
  });
};

// Check if device supports hardware acceleration
export const supportsHardwareAcceleration = () => {
  const canvas = document.createElement('canvas');
  const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
  return !!gl;
};

// Optimize animations based on device capabilities
export const getAnimationConfig = () => {
  const isLowEndDevice = navigator.hardwareConcurrency <= 2;
  const prefersReduced = prefersReducedMotion();
  
  if (prefersReduced) {
    return {
      duration: 0.1,
      ease: "none",
      stagger: 0
    };
  }
  
  if (isLowEndDevice) {
    return {
      duration: 0.5,
      ease: "power1.out",
      stagger: 0.1
    };
  }
  
  return {
    duration: 1,
    ease: "power3.out",
    stagger: 0.2
  };
};
