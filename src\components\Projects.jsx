import { useEffect, useRef } from "react";
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import { motion } from "framer-motion";

gsap.registerPlugin(ScrollTrigger);

const Projects = () => {
  const sectionRef = useRef(null);
  const titleRef = useRef(null);
  const projectsRef = useRef([]);

  useEffect(() => {
    const section = sectionRef.current;
    const title = titleRef.current;
    const projects = projectsRef.current;

    if (!section || !title) return;

    // Title animation
    gsap.fromTo(
      title,
      { y: 100, opacity: 0 },
      {
        y: 0,
        opacity: 1,
        duration: 1.2,
        ease: "power3.out",
        scrollTrigger: {
          trigger: title,
          start: "top 80%",
          end: "bottom 20%",
          toggleActions: "play none none reverse",
        },
      }
    );

    // Projects stagger animation
    gsap.fromTo(
      projects,
      { y: 120, opacity: 0, rotationX: 45 },
      {
        y: 0,
        opacity: 1,
        rotationX: 0,
        duration: 1,
        ease: "power3.out",
        stagger: 0.3,
        scrollTrigger: {
          trigger: projects[0],
          start: "top 85%",
          end: "bottom 20%",
          toggleActions: "play none none reverse",
        },
      }
    );

    return () => {
      ScrollTrigger.getAll().forEach((trigger) => trigger.kill());
    };
  }, []);

  const projects = [
    {
      title: "EcoTech Solutions",
      description:
        "A sustainable technology platform that grew from a simple idea into a comprehensive ecosystem.",
      category: "Web Platform",
      color: "var(--accent-cyan)",
    },
    {
      title: "Artisan Marketplace",
      description:
        "Connecting local artisans with global audiences through beautiful, intuitive design.",
      category: "E-commerce",
      color: "var(--accent-magenta)",
    },
    {
      title: "MindFlow App",
      description:
        "A meditation and wellness app that blossomed into a community of mindful individuals.",
      category: "Mobile App",
      color: "var(--accent-purple)",
    },
    {
      title: "Urban Gardens",
      description:
        "Bringing nature back to the city through innovative urban planning and design.",
      category: "Brand Identity",
      color: "var(--accent-orange)",
    },
  ];

  return (
    <section
      id="projects"
      ref={sectionRef}
      style={{
        minHeight: "100vh",
        padding: "8rem 2rem",
        background: "var(--primary-bg)",
        position: "relative",
      }}
    >
      <div
        style={{
          maxWidth: "1400px",
          margin: "0 auto",
        }}
      >
        <motion.h2
          ref={titleRef}
          style={{
            textAlign: "center",
            marginBottom: "6rem",
            background: `linear-gradient(135deg, var(--accent-orange), var(--accent-cyan))`,
            backgroundClip: "text",
            WebkitBackgroundClip: "text",
            WebkitTextFillColor: "transparent",
          }}
        >
          Seeds That Became Forests
        </motion.h2>

        <div
          style={{
            display: "grid",
            gridTemplateColumns: "repeat(auto-fit, minmax(350px, 1fr))",
            gap: "3rem",
          }}
        >
          {projects.map((project, index) => (
            <motion.div
              key={index}
              ref={(el) => (projectsRef.current[index] = el)}
              style={{
                background:
                  "linear-gradient(135deg, rgba(26, 26, 26, 0.8), rgba(10, 10, 10, 0.9))",
                borderRadius: "24px",
                padding: "3rem",
                border: `1px solid ${project.color}20`,
                position: "relative",
                overflow: "hidden",
                backdropFilter: "blur(20px)",
                cursor: "pointer",
              }}
              whileHover={{
                scale: 1.02,
                y: -10,
                boxShadow: `0 30px 60px ${project.color}30`,
              }}
              transition={{ duration: 0.4, ease: "easeOut" }}
              data-cursor-hover
            >
              {/* Animated background gradient */}
              <motion.div
                style={{
                  position: "absolute",
                  top: 0,
                  left: 0,
                  right: 0,
                  height: "4px",
                  background: `linear-gradient(90deg, ${project.color}, transparent)`,
                  borderRadius: "24px 24px 0 0",
                }}
                initial={{ width: "0%" }}
                whileInView={{ width: "100%" }}
                transition={{ duration: 1.5, delay: index * 0.2 }}
              />

              <div
                style={{
                  fontSize: "0.9rem",
                  color: project.color,
                  fontWeight: "600",
                  marginBottom: "1rem",
                  textTransform: "uppercase",
                  letterSpacing: "0.1em",
                }}
              >
                {project.category}
              </div>

              <h3
                style={{
                  marginBottom: "1.5rem",
                  color: "var(--text-primary)",
                  fontSize: "clamp(1.5rem, 2.5vw, 2rem)",
                }}
              >
                {project.title}
              </h3>

              <p
                style={{
                  color: "var(--text-secondary)",
                  lineHeight: 1.7,
                  marginBottom: "2rem",
                }}
              >
                {project.description}
              </p>

              <motion.div
                style={{
                  display: "flex",
                  alignItems: "center",
                  color: project.color,
                  fontWeight: "600",
                  fontSize: "0.95rem",
                }}
                whileHover={{ x: 10 }}
                transition={{ duration: 0.3 }}
              >
                View Project
                <motion.span
                  style={{ marginLeft: "0.5rem" }}
                  animate={{ x: [0, 5, 0] }}
                  transition={{ duration: 2, repeat: Infinity }}
                >
                  →
                </motion.span>
              </motion.div>

              {/* Hover effect overlay */}
              <motion.div
                style={{
                  position: "absolute",
                  top: 0,
                  left: "-100%",
                  width: "100%",
                  height: "100%",
                  background: `linear-gradient(90deg, transparent, ${project.color}10, transparent)`,
                  pointerEvents: "none",
                }}
                whileHover={{
                  left: "100%",
                  transition: { duration: 0.8, ease: "easeInOut" },
                }}
              />
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Projects;
