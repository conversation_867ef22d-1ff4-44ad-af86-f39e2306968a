import { useEffect, useRef } from "react";
import { gsap } from "gsap";
import { throttle, prefersReducedMotion } from "../utils/performance";

const CustomCursor = () => {
  const cursorRef = useRef(null);
  const cursorDotRef = useRef(null);

  useEffect(() => {
    const cursor = cursorRef.current;
    const cursorDot = cursorDotRef.current;

    if (!cursor || !cursorDot || prefersReducedMotion()) return;

    // Set initial position with hardware acceleration
    gsap.set(cursor, {
      xPercent: -50,
      yPercent: -50,
      force3D: true,
    });
    gsap.set(cursorDot, {
      xPercent: -50,
      yPercent: -50,
      force3D: true,
    });

    const moveCursor = throttle((e) => {
      gsap.to(cursor, {
        duration: 0.6,
        x: e.clientX,
        y: e.clientY,
        ease: "power2.out",
        force3D: true,
      });

      gsap.to(cursorDot, {
        duration: 0.1,
        x: e.clientX,
        y: e.clientY,
        ease: "power2.out",
        force3D: true,
      });
    }, 16); // ~60fps

    const handleMouseEnter = () => {
      gsap.to(cursor, {
        duration: 0.3,
        scale: 1.5,
        backgroundColor: "rgba(236, 72, 153, 0.3)",
        ease: "power2.out",
      });
    };

    const handleMouseLeave = () => {
      gsap.to(cursor, {
        duration: 0.3,
        scale: 1,
        backgroundColor: "rgba(139, 92, 246, 0.2)",
        ease: "power2.out",
      });
    };

    // Add event listeners
    document.addEventListener("mousemove", moveCursor);

    // Add hover effects for interactive elements
    const interactiveElements = document.querySelectorAll(
      "button, a, [data-cursor-hover]"
    );
    interactiveElements.forEach((el) => {
      el.addEventListener("mouseenter", handleMouseEnter);
      el.addEventListener("mouseleave", handleMouseLeave);
    });

    return () => {
      document.removeEventListener("mousemove", moveCursor);
      interactiveElements.forEach((el) => {
        el.removeEventListener("mouseenter", handleMouseEnter);
        el.removeEventListener("mouseleave", handleMouseLeave);
      });
    };
  }, []);

  return (
    <>
      <div
        ref={cursorRef}
        className="custom-cursor"
        style={{
          position: "fixed",
          top: 0,
          left: 0,
          width: "40px",
          height: "40px",
          backgroundColor: "rgba(139, 92, 246, 0.2)",
          borderRadius: "50%",
          pointerEvents: "none",
          zIndex: 9999,
          mixBlendMode: "difference",
          border: "1px solid rgba(139, 92, 246, 0.5)",
        }}
      />
      <div
        ref={cursorDotRef}
        className="cursor-dot"
        style={{
          position: "fixed",
          top: 0,
          left: 0,
          width: "4px",
          height: "4px",
          backgroundColor: "#8b5cf6",
          borderRadius: "50%",
          pointerEvents: "none",
          zIndex: 10000,
        }}
      />
    </>
  );
};

export default CustomCursor;
