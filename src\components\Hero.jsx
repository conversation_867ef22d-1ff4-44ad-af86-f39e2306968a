import { useEffect, useRef } from "react";
import { gsap } from "gsap";
import { motion } from "framer-motion";

const Hero = () => {
  const heroRef = useRef(null);
  const titleRef = useRef(null);
  const subtitleRef = useRef(null);
  const backgroundRef = useRef(null);

  useEffect(() => {
    const hero = heroRef.current;
    const title = titleRef.current;
    const subtitle = subtitleRef.current;
    const background = backgroundRef.current;

    if (!hero || !title || !subtitle || !background) return;

    // Initial animation timeline
    const tl = gsap.timeline();

    // Set initial states
    gsap.set([title, subtitle], { y: 100, opacity: 0 });
    gsap.set(background, { scale: 0.8, opacity: 0 });

    // Animate elements in sequence
    tl.to(background, {
      duration: 1.5,
      scale: 1,
      opacity: 1,
      ease: "power3.out",
    })
      .to(
        title,
        {
          duration: 1.2,
          y: 0,
          opacity: 1,
          ease: "power3.out",
        },
        "-=0.8"
      )
      .to(
        subtitle,
        {
          duration: 1,
          y: 0,
          opacity: 1,
          ease: "power3.out",
        },
        "-=0.6"
      );

    // Continuous background animation
    gsap.to(background, {
      rotation: 360,
      duration: 20,
      ease: "none",
      repeat: -1,
    });

    // Parallax effect on scroll
    const handleScroll = () => {
      const scrollY = window.scrollY;
      const heroHeight = hero.offsetHeight;
      const progress = Math.min(scrollY / heroHeight, 1);

      gsap.to(title, {
        duration: 0.3,
        y: scrollY * 0.5,
        opacity: 1 - progress * 0.8,
        ease: "none",
      });

      gsap.to(subtitle, {
        duration: 0.3,
        y: scrollY * 0.3,
        opacity: 1 - progress * 0.6,
        ease: "none",
      });
    };

    window.addEventListener("scroll", handleScroll);

    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);

  return (
    <section
      id="hero"
      ref={heroRef}
      className="hero-section"
      style={{
        height: "100vh",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        position: "relative",
        overflow: "hidden",
        background:
          "linear-gradient(135deg, var(--primary-bg) 0%, var(--secondary-bg) 100%)",
      }}
    >
      {/* Animated Background */}
      <div
        ref={backgroundRef}
        style={{
          position: "absolute",
          top: "50%",
          left: "50%",
          transform: "translate(-50%, -50%)",
          width: "120vw",
          height: "120vh",
          background: `
            radial-gradient(circle at 30% 70%, var(--accent-purple) 0%, transparent 50%),
            radial-gradient(circle at 70% 30%, var(--accent-magenta) 0%, transparent 50%),
            radial-gradient(circle at 50% 50%, var(--accent-cyan) 0%, transparent 70%)
          `,
          opacity: 0.1,
          filter: "blur(100px)",
          zIndex: 1,
        }}
      />

      {/* Content */}
      <div
        style={{
          textAlign: "center",
          zIndex: 2,
          position: "relative",
          maxWidth: "1200px",
          padding: "0 2rem",
        }}
      >
        <motion.h1
          ref={titleRef}
          style={{
            background: `linear-gradient(135deg, var(--accent-purple), var(--accent-magenta), var(--accent-cyan))`,
            backgroundClip: "text",
            WebkitBackgroundClip: "text",
            WebkitTextFillColor: "transparent",
            marginBottom: "2rem",
          }}
          whileHover={{ scale: 1.05 }}
          transition={{ duration: 0.3 }}
        >
          We build your ideas from the roots
        </motion.h1>

        <motion.p
          ref={subtitleRef}
          style={{
            fontSize: "clamp(1.2rem, 3vw, 2rem)",
            color: "var(--text-secondary)",
            maxWidth: "600px",
            margin: "0 auto 3rem",
            lineHeight: 1.6,
          }}
          whileHover={{ color: "var(--text-primary)" }}
          transition={{ duration: 0.3 }}
        >
          Crafting digital experiences that grow from concept to reality
        </motion.p>

        <motion.button
          style={{
            background:
              "linear-gradient(135deg, var(--accent-purple), var(--accent-magenta))",
            border: "none",
            padding: "1rem 2.5rem",
            borderRadius: "50px",
            color: "white",
            fontSize: "1.1rem",
            fontWeight: "600",
            cursor: "pointer",
            position: "relative",
            overflow: "hidden",
          }}
          whileHover={{
            scale: 1.05,
            boxShadow: "0 20px 40px rgba(139, 92, 246, 0.3)",
          }}
          whileTap={{ scale: 0.95 }}
          data-cursor-hover
        >
          <span style={{ position: "relative", zIndex: 2 }}>
            Explore Our Work
          </span>
          <motion.div
            style={{
              position: "absolute",
              top: 0,
              left: "-100%",
              width: "100%",
              height: "100%",
              background:
                "linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent)",
              zIndex: 1,
            }}
            animate={{
              left: ["100%", "-100%"],
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: "linear",
            }}
          />
        </motion.button>
      </div>

      {/* Scroll indicator */}
      <motion.div
        style={{
          position: "absolute",
          bottom: "2rem",
          left: "50%",
          transform: "translateX(-50%)",
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          color: "var(--text-muted)",
        }}
        animate={{ y: [0, 10, 0] }}
        transition={{ duration: 2, repeat: Infinity }}
      >
        <span style={{ fontSize: "0.9rem", marginBottom: "0.5rem" }}>
          Scroll to explore
        </span>
        <div
          style={{
            width: "2px",
            height: "30px",
            background:
              "linear-gradient(to bottom, var(--accent-purple), transparent)",
            borderRadius: "1px",
          }}
        />
      </motion.div>
    </section>
  );
};

export default Hero;
