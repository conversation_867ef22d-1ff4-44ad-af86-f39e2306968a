import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

const Navigation = () => {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const scrollToSection = (sectionId) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
      setIsMenuOpen(false);
    }
  };

  const navItems = [
    { label: 'Home', id: 'hero' },
    { label: 'About', id: 'about' },
    { label: 'Projects', id: 'projects' },
    { label: 'Contact', id: 'contact' }
  ];

  return (
    <motion.nav
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        zIndex: 1000,
        padding: '1rem 2rem',
        background: isScrolled 
          ? 'rgba(10, 10, 10, 0.9)' 
          : 'transparent',
        backdropFilter: isScrolled ? 'blur(20px)' : 'none',
        borderBottom: isScrolled 
          ? '1px solid rgba(139, 92, 246, 0.2)' 
          : 'none',
        transition: 'all 0.3s ease'
      }}
      initial={{ y: -100 }}
      animate={{ y: 0 }}
      transition={{ duration: 0.8, ease: "easeOut" }}
    >
      <div style={{
        maxWidth: '1400px',
        margin: '0 auto',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center'
      }}>
        {/* Logo */}
        <motion.div
          style={{
            fontSize: '1.5rem',
            fontWeight: '800',
            background: 'linear-gradient(135deg, var(--accent-purple), var(--accent-magenta))',
            backgroundClip: 'text',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            cursor: 'pointer'
          }}
          whileHover={{ scale: 1.05 }}
          onClick={() => scrollToSection('hero')}
          data-cursor-hover
        >
          Bindery
        </motion.div>

        {/* Desktop Navigation */}
        <div style={{
          display: 'flex',
          gap: '2rem',
          alignItems: 'center'
        }}>
          {navItems.map((item, index) => (
            <motion.button
              key={item.id}
              style={{
                background: 'transparent',
                border: 'none',
                color: 'var(--text-secondary)',
                fontSize: '1rem',
                fontWeight: '500',
                cursor: 'pointer',
                padding: '0.5rem 1rem',
                borderRadius: '8px',
                transition: 'color 0.3s ease'
              }}
              whileHover={{
                color: 'var(--accent-purple)',
                scale: 1.05
              }}
              whileTap={{ scale: 0.95 }}
              onClick={() => scrollToSection(item.id)}
              data-cursor-hover
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
            >
              {item.label}
            </motion.button>
          ))}

          {/* CTA Button */}
          <motion.button
            style={{
              background: 'linear-gradient(135deg, var(--accent-purple), var(--accent-magenta))',
              border: 'none',
              color: 'white',
              padding: '0.8rem 1.5rem',
              borderRadius: '50px',
              fontSize: '0.95rem',
              fontWeight: '600',
              cursor: 'pointer'
            }}
            whileHover={{
              scale: 1.05,
              boxShadow: '0 10px 25px rgba(139, 92, 246, 0.3)'
            }}
            whileTap={{ scale: 0.95 }}
            onClick={() => scrollToSection('contact')}
            data-cursor-hover
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5, delay: 0.4 }}
          >
            Start Project
          </motion.button>
        </div>

        {/* Mobile Menu Button */}
        <motion.button
          style={{
            display: 'none',
            background: 'transparent',
            border: 'none',
            color: 'var(--text-primary)',
            fontSize: '1.5rem',
            cursor: 'pointer',
            padding: '0.5rem'
          }}
          onClick={() => setIsMenuOpen(!isMenuOpen)}
          data-cursor-hover
          className="mobile-menu-btn"
        >
          {isMenuOpen ? '✕' : '☰'}
        </motion.button>
      </div>

      {/* Mobile Menu */}
      <AnimatePresence>
        {isMenuOpen && (
          <motion.div
            style={{
              position: 'absolute',
              top: '100%',
              left: 0,
              right: 0,
              background: 'rgba(10, 10, 10, 0.95)',
              backdropFilter: 'blur(20px)',
              border: '1px solid rgba(139, 92, 246, 0.2)',
              borderTop: 'none',
              padding: '2rem',
              display: 'none'
            }}
            className="mobile-menu"
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
          >
            <div style={{
              display: 'flex',
              flexDirection: 'column',
              gap: '1rem'
            }}>
              {navItems.map((item) => (
                <motion.button
                  key={item.id}
                  style={{
                    background: 'transparent',
                    border: 'none',
                    color: 'var(--text-secondary)',
                    fontSize: '1.1rem',
                    fontWeight: '500',
                    cursor: 'pointer',
                    padding: '1rem',
                    textAlign: 'left',
                    borderRadius: '8px'
                  }}
                  whileHover={{ color: 'var(--accent-purple)' }}
                  onClick={() => scrollToSection(item.id)}
                  data-cursor-hover
                >
                  {item.label}
                </motion.button>
              ))}
              
              <motion.button
                style={{
                  background: 'linear-gradient(135deg, var(--accent-purple), var(--accent-magenta))',
                  border: 'none',
                  color: 'white',
                  padding: '1rem',
                  borderRadius: '12px',
                  fontSize: '1rem',
                  fontWeight: '600',
                  cursor: 'pointer',
                  marginTop: '1rem'
                }}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={() => scrollToSection('contact')}
                data-cursor-hover
              >
                Start Project
              </motion.button>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      <style jsx>{`
        @media (max-width: 768px) {
          .mobile-menu-btn {
            display: block !important;
          }
          
          .mobile-menu {
            display: block !important;
          }
          
          nav > div > div:nth-child(2) {
            display: none !important;
          }
        }
      `}</style>
    </motion.nav>
  );
};

export default Navigation;
