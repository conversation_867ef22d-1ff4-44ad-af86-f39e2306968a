import { useEffect, useRef } from "react";
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import { motion } from "framer-motion";

gsap.registerPlugin(ScrollTrigger);

const About = () => {
  const sectionRef = useRef(null);
  const titleRef = useRef(null);
  const contentRef = useRef(null);
  const cardsRef = useRef([]);

  useEffect(() => {
    const section = sectionRef.current;
    const title = titleRef.current;
    const content = contentRef.current;
    const cards = cardsRef.current;

    if (!section || !title || !content) return;

    // Title animation
    gsap.fromTo(
      title,
      { y: 100, opacity: 0 },
      {
        y: 0,
        opacity: 1,
        duration: 1.2,
        ease: "power3.out",
        scrollTrigger: {
          trigger: title,
          start: "top 80%",
          end: "bottom 20%",
          toggleActions: "play none none reverse",
        },
      }
    );

    // Content animation
    gsap.fromTo(
      content,
      { y: 80, opacity: 0 },
      {
        y: 0,
        opacity: 1,
        duration: 1,
        ease: "power3.out",
        scrollTrigger: {
          trigger: content,
          start: "top 85%",
          end: "bottom 20%",
          toggleActions: "play none none reverse",
        },
      }
    );

    // Cards stagger animation
    gsap.fromTo(
      cards,
      { y: 100, opacity: 0, scale: 0.8 },
      {
        y: 0,
        opacity: 1,
        scale: 1,
        duration: 0.8,
        ease: "power3.out",
        stagger: 0.2,
        scrollTrigger: {
          trigger: cards[0],
          start: "top 90%",
          end: "bottom 20%",
          toggleActions: "play none none reverse",
        },
      }
    );

    return () => {
      ScrollTrigger.getAll().forEach((trigger) => trigger.kill());
    };
  }, []);

  const services = [
    {
      title: "Digital Strategy",
      description:
        "We plant the seeds of your digital presence with strategic planning and brand positioning.",
      icon: "🌱",
    },
    {
      title: "Creative Design",
      description:
        "Nurturing your brand's visual identity with stunning designs that captivate and engage.",
      icon: "🎨",
    },
    {
      title: "Development",
      description:
        "Growing your ideas into fully functional digital experiences with cutting-edge technology.",
      icon: "⚡",
    },
  ];

  return (
    <section
      id="about"
      ref={sectionRef}
      style={{
        minHeight: "100vh",
        padding: "8rem 2rem",
        background: "var(--secondary-bg)",
        position: "relative",
      }}
    >
      <div
        style={{
          maxWidth: "1200px",
          margin: "0 auto",
          textAlign: "center",
        }}
      >
        <motion.h2
          ref={titleRef}
          style={{
            marginBottom: "3rem",
            background: `linear-gradient(135deg, var(--accent-cyan), var(--accent-purple))`,
            backgroundClip: "text",
            WebkitBackgroundClip: "text",
            WebkitTextFillColor: "transparent",
          }}
        >
          Growing Ideas Into Reality
        </motion.h2>

        <div
          ref={contentRef}
          style={{
            marginBottom: "6rem",
            maxWidth: "800px",
            margin: "0 auto 6rem",
          }}
        >
          <p
            style={{
              fontSize: "clamp(1.1rem, 2.5vw, 1.5rem)",
              lineHeight: 1.8,
              color: "var(--text-secondary)",
            }}
          >
            Like a gardener tends to their plants, we nurture your digital ideas
            from conception to full bloom. Our approach is rooted in
            understanding your vision and cultivating it with creativity,
            strategy, and technical expertise.
          </p>
        </div>

        <div
          style={{
            display: "grid",
            gridTemplateColumns: "repeat(auto-fit, minmax(300px, 1fr))",
            gap: "2rem",
            marginTop: "4rem",
          }}
        >
          {services.map((service, index) => (
            <motion.div
              key={index}
              ref={(el) => (cardsRef.current[index] = el)}
              style={{
                background:
                  "linear-gradient(135deg, rgba(139, 92, 246, 0.1), rgba(236, 72, 153, 0.1))",
                padding: "3rem 2rem",
                borderRadius: "20px",
                border: "1px solid rgba(139, 92, 246, 0.2)",
                backdropFilter: "blur(10px)",
                position: "relative",
                overflow: "hidden",
              }}
              whileHover={{
                scale: 1.05,
                boxShadow: "0 20px 40px rgba(139, 92, 246, 0.2)",
              }}
              transition={{ duration: 0.3 }}
              data-cursor-hover
            >
              <div
                style={{
                  fontSize: "3rem",
                  marginBottom: "1.5rem",
                }}
              >
                {service.icon}
              </div>

              <h3
                style={{
                  marginBottom: "1rem",
                  color: "var(--text-primary)",
                }}
              >
                {service.title}
              </h3>

              <p
                style={{
                  color: "var(--text-secondary)",
                  lineHeight: 1.6,
                }}
              >
                {service.description}
              </p>

              {/* Hover effect overlay */}
              <motion.div
                style={{
                  position: "absolute",
                  top: 0,
                  left: "-100%",
                  width: "100%",
                  height: "100%",
                  background:
                    "linear-gradient(90deg, transparent, rgba(139, 92, 246, 0.1), transparent)",
                  pointerEvents: "none",
                }}
                whileHover={{
                  left: "100%",
                  transition: { duration: 0.6, ease: "easeInOut" },
                }}
              />
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default About;
